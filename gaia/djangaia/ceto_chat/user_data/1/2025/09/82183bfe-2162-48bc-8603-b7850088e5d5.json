{"conversation_id": "82183bfe-2162-48bc-8603-b7850088e5d5", "user_id": "1", "title": "Conversation 82183bfe", "metadata": {}, "messages": [{"role": "user", "content": "test1", "timestamp": "2025-09-05T00:12:35.512200"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"test1\"", "timestamp": "2025-09-05T00:12:35.512208"}, {"role": "user", "content": "Let's try echostring...", "timestamp": "2025-09-05T00:12:42.249737"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"Let's try echostring...\"", "timestamp": "2025-09-05T00:12:42.249743"}, {"role": "user", "content": "test 3", "timestamp": "2025-09-05T00:12:49.382065"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"test 3\"", "timestamp": "2025-09-05T00:12:49.382069"}, {"role": "user", "content": "/echostring test5", "timestamp": "2025-09-05T00:25:08.606551"}, {"role": "tool", "content": "{\"tool\":\"echostring\",\"rpc\":{\"success\":true,\"text\":\"test5, test5, test5 fer SURE!\",\"rpc\":{\"jsonrpc\":\"2.0\",\"id\":\"2ba8ec73-80c2-494d-9ce7-2bb70dd80c45\",\"result\":{\"content\":[{\"type\":\"text\",\"text\":\"test5, test5, test5 fer SURE!\"}],\"flags\":{\"needs_synthesis\":\"defualt\",\"show_to_user\":true,\"store_as_document\":false}},\"tool_name\":\"echostring\",\"tool_args\":{\"text\":\"test5\"}},\"tool_name\":\"echostring\",\"tool_args\":{\"text\":\"test5\"}}}", "timestamp": "2025-09-05T00:25:08.606555"}, {"role": "user", "content": "test 6", "timestamp": "2025-09-05T00:25:17.225757"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"test 6\"", "timestamp": "2025-09-05T00:25:17.225760"}, {"role": "user", "content": "test 7", "timestamp": "2025-09-05T00:25:21.705527"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"test 7\"", "timestamp": "2025-09-05T00:25:21.705530"}, {"role": "user", "content": "/echostring foo", "timestamp": "2025-09-05T20:39:55.733220"}, {"role": "tool", "content": "{\"tool\":\"echostring\",\"rpc\":{\"success\":true,\"text\":\"foo, foo, foo fer SURE!\",\"rpc\":{\"jsonrpc\":\"2.0\",\"id\":\"b879fee5-05f0-47fd-ba38-44c210e53f9d\",\"result\":{\"content\":[{\"type\":\"text\",\"text\":\"foo, foo, foo fer SURE!\"}],\"flags\":{\"needs_synthesis\":\"defualt\",\"show_to_user\":true,\"store_as_document\":false}},\"tool_name\":\"echostring\",\"tool_args\":{\"text\":\"foo\"}},\"tool_name\":\"echostring\",\"tool_args\":{\"text\":\"foo\"}}}", "timestamp": "2025-09-05T20:39:55.733234"}, {"role": "user", "content": "hello", "timestamp": "2025-09-08T20:39:15.594019"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"hello\"", "timestamp": "2025-09-08T20:39:15.594024"}, {"role": "user", "content": "/echostring foo", "timestamp": "2025-09-08T20:39:25.100183"}, {"role": "tool", "content": "{\"tool\":\"echostring\",\"args\":\"foo\"}", "timestamp": "2025-09-08T20:39:25.100188"}], "created_at": "2025-09-05T00:12:35.492081", "updated_at": "2025-09-08T20:39:25.100188"}