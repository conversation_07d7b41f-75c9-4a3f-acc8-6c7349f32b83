/* Enhanced Style CSS - Elegant and Slick Look for Ceto Chat */
/* Inspired by modern chat interfaces like SpeechGPT */

/* ===== GLOBAL ENHANCEMENTS ===== */

:root {
    /* Modern Color Palette */
    --enhanced-primary: #6366f1;
    --enhanced-primary-light: #8b5cf6;
    --enhanced-primary-dark: #4f46e5;
    --enhanced-secondary: #f1f5f9;
    --enhanced-accent: #10b981;
    --enhanced-warning: #f59e0b;
    --enhanced-danger: #ef4444;
    
    /* Neutral Grays */
    --enhanced-gray-50: #f8fafc;
    --enhanced-gray-100: #f1f5f9;
    --enhanced-gray-200: #e2e8f0;
    --enhanced-gray-300: #cbd5e1;
    --enhanced-gray-400: #94a3b8;
    --enhanced-gray-500: #64748b;
    --enhanced-gray-600: #475569;
    --enhanced-gray-700: #334155;
    --enhanced-gray-800: #1e293b;
    --enhanced-gray-900: #0f172a;
    
    /* Typography */
    --enhanced-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    --enhanced-font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
    
    /* Spacing & Sizing */
    --enhanced-border-radius: 12px;
    --enhanced-border-radius-sm: 8px;
    --enhanced-border-radius-lg: 16px;
    --enhanced-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --enhanced-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --enhanced-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --enhanced-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --enhanced-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --enhanced-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --enhanced-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Body & Container */
.enhanced-theme body {
    font-family: var(--enhanced-font-family);
    background: linear-gradient(135deg, var(--enhanced-gray-50) 0%, var(--enhanced-gray-100) 100%);
    color: var(--enhanced-gray-800);
    line-height: 1.6;
}

.enhanced-theme .app-container {
    background: transparent;
    gap: 0;
}

/* ===== SIDEBAR ENHANCEMENTS ===== */

.enhanced-theme .sidebar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid var(--enhanced-gray-200);
    border-radius: 0 var(--enhanced-border-radius) var(--enhanced-border-radius) 0;
    box-shadow: var(--enhanced-shadow-lg);
    padding: 24px 20px;
    width: 320px;
    transition: var(--enhanced-transition);
}

.enhanced-theme .sidebar:hover {
    box-shadow: var(--enhanced-shadow-xl);
}

/* Enhanced Conversation Items */
.enhanced-theme .conv-item {
    background: var(--enhanced-gray-50);
    border: 1px solid var(--enhanced-gray-200);
    border-radius: var(--enhanced-border-radius-sm);
    padding: 12px 16px;
    margin: 8px 0;
    transition: var(--enhanced-transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.enhanced-theme .conv-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--enhanced-primary);
    transform: scaleY(0);
    transition: var(--enhanced-transition);
}

.enhanced-theme .conv-item:hover {
    background: var(--enhanced-gray-100);
    border-color: var(--enhanced-primary);
    transform: translateY(-1px);
    box-shadow: var(--enhanced-shadow-md);
}

.enhanced-theme .conv-item:hover::before {
    transform: scaleY(1);
}

.enhanced-theme .conv-item.active {
    background: linear-gradient(135deg, var(--enhanced-primary) 0%, var(--enhanced-primary-light) 100%);
    border-color: var(--enhanced-primary-dark);
    color: white;
    box-shadow: var(--enhanced-shadow-md);
}

.enhanced-theme .conv-item.active::before {
    transform: scaleY(1);
    background: rgba(255, 255, 255, 0.3);
}

.enhanced-theme .conv-title {
    font-weight: 500;
    font-size: 14px;
    line-height: 1.4;
}

.enhanced-theme .conv-item.active .conv-title {
    color: white;
}

.enhanced-theme .conv-delete {
    background: transparent;
    border: none;
    color: var(--enhanced-gray-400);
    font-size: 16px;
    padding: 4px 8px;
    border-radius: var(--enhanced-border-radius-sm);
    transition: var(--enhanced-transition-fast);
}

.enhanced-theme .conv-delete:hover {
    background: var(--enhanced-danger);
    color: white;
    transform: scale(1.1);
}

.enhanced-theme .conv-item.active .conv-delete {
    color: rgba(255, 255, 255, 0.8);
}

.enhanced-theme .conv-item.active .conv-delete:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* ===== MAIN CONTENT ENHANCEMENTS ===== */

.enhanced-theme .main-content {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid var(--enhanced-gray-200);
    border-radius: var(--enhanced-border-radius) 0 0 var(--enhanced-border-radius);
    margin: 0;
    padding: 32px;
    box-shadow: var(--enhanced-shadow-lg);
    transition: var(--enhanced-transition);
}

/* Enhanced Headers */
.enhanced-theme h1 {
    color: var(--enhanced-gray-900);
    font-weight: 700;
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-align: center;
    background: linear-gradient(135deg, var(--enhanced-primary) 0%, var(--enhanced-primary-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.enhanced-theme h2 {
    color: var(--enhanced-gray-800);
    font-weight: 600;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.enhanced-theme h3 {
    color: var(--enhanced-gray-700);
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
}

/* ===== BUTTON ENHANCEMENTS ===== */

.enhanced-theme .btn {
    font-family: var(--enhanced-font-family);
    font-weight: 500;
    border-radius: var(--enhanced-border-radius-sm);
    padding: 10px 20px;
    transition: var(--enhanced-transition);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.enhanced-theme .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--enhanced-transition);
}

.enhanced-theme .btn:hover::before {
    left: 100%;
}

.enhanced-theme .btn-primary {
    background: linear-gradient(135deg, var(--enhanced-primary) 0%, var(--enhanced-primary-light) 100%);
    color: white;
    box-shadow: var(--enhanced-shadow);
}

.enhanced-theme .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--enhanced-shadow-lg);
}

.enhanced-theme .btn-secondary {
    background: var(--enhanced-gray-100);
    color: var(--enhanced-gray-700);
    border: 1px solid var(--enhanced-gray-300);
}

.enhanced-theme .btn-secondary:hover {
    background: var(--enhanced-gray-200);
    border-color: var(--enhanced-gray-400);
    transform: translateY(-1px);
}

.enhanced-theme .btn-success {
    background: linear-gradient(135deg, var(--enhanced-accent) 0%, #059669 100%);
    color: white;
    box-shadow: var(--enhanced-shadow);
}

.enhanced-theme .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--enhanced-shadow-lg);
}

.enhanced-theme .btn-warning {
    background: linear-gradient(135deg, var(--enhanced-warning) 0%, #d97706 100%);
    color: white;
    box-shadow: var(--enhanced-shadow);
}

.enhanced-theme .btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: var(--enhanced-shadow-lg);
}

.enhanced-theme .btn-danger {
    background: linear-gradient(135deg, var(--enhanced-danger) 0%, #dc2626 100%);
    color: white;
    box-shadow: var(--enhanced-shadow);
}

.enhanced-theme .btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: var(--enhanced-shadow-lg);
}

.enhanced-theme .btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
    border-radius: 6px;
}

.enhanced-theme .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.enhanced-theme .btn:disabled:hover {
    transform: none !important;
    box-shadow: var(--enhanced-shadow) !important;
}

/* ===== FORM ENHANCEMENTS ===== */

.enhanced-theme input[type="text"],
.enhanced-theme input[type="email"],
.enhanced-theme input[type="password"],
.enhanced-theme textarea,
.enhanced-theme select {
    font-family: var(--enhanced-font-family);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--enhanced-gray-300);
    border-radius: var(--enhanced-border-radius-sm);
    padding: 12px 16px;
    font-size: 14px;
    transition: var(--enhanced-transition);
    box-shadow: var(--enhanced-shadow-sm);
    color: var(--enhanced-gray-800);
}

.enhanced-theme input[type="text"]:focus,
.enhanced-theme input[type="email"]:focus,
.enhanced-theme input[type="password"]:focus,
.enhanced-theme textarea:focus,
.enhanced-theme select:focus {
    outline: none;
    border-color: var(--enhanced-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--enhanced-shadow);
    background: rgba(255, 255, 255, 1);
}

.enhanced-theme .chat-input-row {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid var(--enhanced-gray-200);
    border-radius: var(--enhanced-border-radius);
    padding: 16px 20px;
    margin: 20px 0;
    box-shadow: var(--enhanced-shadow-md);
    gap: 12px;
}

.enhanced-theme .chat-input-row input[type="text"] {
    border: 1px solid var(--enhanced-gray-300);
    background: var(--enhanced-gray-50);
    flex: 1;
    min-width: 0;
}

.enhanced-theme .chat-input-row input[type="text"]:focus {
    background: white;
    border-color: var(--enhanced-primary);
}

/* ===== CHAT MESSAGE ENHANCEMENTS ===== */

.enhanced-theme .message-item {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--enhanced-gray-200);
    border-radius: var(--enhanced-border-radius);
    padding: 16px 20px;
    margin: 12px 0;
    box-shadow: var(--enhanced-shadow);
    transition: var(--enhanced-transition);
    position: relative;
    overflow: hidden;
}

.enhanced-theme .message-item:hover {
    box-shadow: var(--enhanced-shadow-md);
    transform: translateY(-1px);
}

.enhanced-theme .message-item .role {
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--enhanced-primary);
    margin-bottom: 8px;
    display: block;
}

.enhanced-theme .message-item .role.user {
    color: var(--enhanced-accent);
}

.enhanced-theme .message-item .role.assistant {
    color: var(--enhanced-primary);
}

.enhanced-theme .message-item .role.system {
    color: var(--enhanced-warning);
}

.enhanced-theme .response-box {
    background: var(--enhanced-gray-50);
    border: 1px solid var(--enhanced-gray-200);
    border-radius: var(--enhanced-border-radius-sm);
    padding: 16px 20px;
    margin: 12px 0;
    font-family: var(--enhanced-font-mono);
    font-size: 13px;
    line-height: 1.5;
    box-shadow: var(--enhanced-shadow-sm);
    position: relative;
}

.enhanced-theme .response-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--enhanced-primary);
    border-radius: 2px 0 0 2px;
}

/* ===== TOOL RESPONSE ENHANCEMENTS ===== */

.enhanced-theme .tool-response {
    background: linear-gradient(135deg, var(--enhanced-gray-50) 0%, var(--enhanced-gray-100) 100%);
    border: 1px solid var(--enhanced-gray-300);
    border-radius: var(--enhanced-border-radius);
    margin: 16px 0;
    overflow: hidden;
    box-shadow: var(--enhanced-shadow-md);
}

.enhanced-theme .tool-response-header {
    background: linear-gradient(135deg, var(--enhanced-primary) 0%, var(--enhanced-primary-light) 100%);
    color: white;
    padding: 12px 20px;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.enhanced-theme .tool-response-header::before {
    content: '🔧';
    font-size: 16px;
}

.enhanced-theme .tool-response-content {
    padding: 20px;
    background: rgba(255, 255, 255, 0.8);
    font-family: var(--enhanced-font-mono);
    font-size: 13px;
    line-height: 1.6;
    color: var(--enhanced-gray-800);
}

/* ===== BADGE ENHANCEMENTS ===== */

.enhanced-theme .badge {
    background: linear-gradient(135deg, var(--enhanced-primary) 0%, var(--enhanced-primary-light) 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--enhanced-shadow-sm);
}

.enhanced-theme .badge-danger {
    background: linear-gradient(135deg, var(--enhanced-danger) 0%, #dc2626 100%);
}

.enhanced-theme .badge-success {
    background: linear-gradient(135deg, var(--enhanced-accent) 0%, #059669 100%);
}

.enhanced-theme .badge-warning {
    background: linear-gradient(135deg, var(--enhanced-warning) 0%, #d97706 100%);
}

.enhanced-theme .badge-info {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

/* ===== DEBUG PANEL ENHANCEMENTS ===== */

.enhanced-theme .debug-panel {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid var(--enhanced-gray-700);
    border-radius: var(--enhanced-border-radius) 0 0 var(--enhanced-border-radius);
    box-shadow: var(--enhanced-shadow-xl);
}

.enhanced-theme .debug-toggle {
    background: linear-gradient(135deg, var(--enhanced-gray-800) 0%, var(--enhanced-gray-900) 100%) !important;
    border: 1px solid var(--enhanced-gray-600) !important;
    color: white !important;
    border-radius: var(--enhanced-border-radius-sm) !important;
    box-shadow: var(--enhanced-shadow-md) !important;
    transition: var(--enhanced-transition) !important;
}

.enhanced-theme .debug-toggle:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--enhanced-shadow-lg) !important;
}

.enhanced-theme .debug-toggle.btn-warning {
    background: linear-gradient(135deg, var(--enhanced-warning) 0%, #d97706 100%) !important;
}

.enhanced-theme .debug-tab {
    background: var(--enhanced-gray-800);
    border: 1px solid var(--enhanced-gray-600);
    color: var(--enhanced-gray-300);
    border-radius: var(--enhanced-border-radius-sm) var(--enhanced-border-radius-sm) 0 0;
    transition: var(--enhanced-transition);
}

.enhanced-theme .debug-tab.active {
    background: linear-gradient(135deg, var(--enhanced-primary) 0%, var(--enhanced-primary-light) 100%);
    color: white;
    border-color: var(--enhanced-primary);
}

.enhanced-theme .debug-tab:hover:not(.active) {
    background: var(--enhanced-gray-700);
    color: white;
}

/* ===== LOADING & ANIMATION ENHANCEMENTS ===== */

.enhanced-theme .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--enhanced-gray-300);
    border-top: 2px solid var(--enhanced-primary);
    border-radius: 50%;
    animation: enhanced-spin 1s linear infinite;
}

@keyframes enhanced-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.enhanced-theme .fade-in {
    animation: enhanced-fade-in 0.3s ease-out;
}

@keyframes enhanced-fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.enhanced-theme .slide-in {
    animation: enhanced-slide-in 0.3s ease-out;
}

@keyframes enhanced-slide-in {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== RESPONSIVE DESIGN ENHANCEMENTS ===== */

@media (max-width: 1200px) {
    .enhanced-theme .sidebar {
        width: 280px;
        padding: 20px 16px;
    }

    .enhanced-theme .main-content {
        padding: 24px;
    }

    .enhanced-theme h1 {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .enhanced-theme .app-container {
        flex-direction: column;
    }

    .enhanced-theme .sidebar {
        width: 100%;
        border-radius: var(--enhanced-border-radius) var(--enhanced-border-radius) 0 0;
        padding: 16px;
        max-height: 200px;
        overflow-y: auto;
    }

    .enhanced-theme .main-content {
        border-radius: 0 0 var(--enhanced-border-radius) var(--enhanced-border-radius);
        padding: 20px 16px;
    }

    .enhanced-theme .chat-input-row {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }

    .enhanced-theme .chat-input-row input[type="text"] {
        width: 100%;
    }

    .enhanced-theme h1 {
        font-size: 1.75rem;
        margin-bottom: 1.5rem;
    }

    .enhanced-theme .conv-item {
        padding: 10px 12px;
        margin: 6px 0;
    }

    .enhanced-theme .message-item {
        padding: 12px 16px;
        margin: 10px 0;
    }
}

@media (max-width: 480px) {
    .enhanced-theme .main-content {
        padding: 16px 12px;
    }

    .enhanced-theme .chat-input-row {
        padding: 12px;
    }

    .enhanced-theme h1 {
        font-size: 1.5rem;
    }

    .enhanced-theme .btn {
        padding: 8px 16px;
        font-size: 14px;
    }

    .enhanced-theme .btn-sm {
        padding: 6px 10px;
        font-size: 12px;
    }
}

/* ===== UTILITY CLASSES ===== */

.enhanced-theme .text-center {
    text-align: center;
}

.enhanced-theme .text-left {
    text-align: left;
}

.enhanced-theme .text-right {
    text-align: right;
}

.enhanced-theme .font-bold {
    font-weight: 700;
}

.enhanced-theme .font-semibold {
    font-weight: 600;
}

.enhanced-theme .font-medium {
    font-weight: 500;
}

.enhanced-theme .text-sm {
    font-size: 0.875rem;
}

.enhanced-theme .text-xs {
    font-size: 0.75rem;
}

.enhanced-theme .text-lg {
    font-size: 1.125rem;
}

.enhanced-theme .text-xl {
    font-size: 1.25rem;
}

.enhanced-theme .text-primary {
    color: var(--enhanced-primary);
}

.enhanced-theme .text-secondary {
    color: var(--enhanced-gray-600);
}

.enhanced-theme .text-success {
    color: var(--enhanced-accent);
}

.enhanced-theme .text-warning {
    color: var(--enhanced-warning);
}

.enhanced-theme .text-danger {
    color: var(--enhanced-danger);
}

.enhanced-theme .bg-primary {
    background-color: var(--enhanced-primary);
}

.enhanced-theme .bg-secondary {
    background-color: var(--enhanced-gray-100);
}

.enhanced-theme .bg-success {
    background-color: var(--enhanced-accent);
}

.enhanced-theme .bg-warning {
    background-color: var(--enhanced-warning);
}

.enhanced-theme .bg-danger {
    background-color: var(--enhanced-danger);
}

.enhanced-theme .rounded {
    border-radius: var(--enhanced-border-radius-sm);
}

.enhanced-theme .rounded-lg {
    border-radius: var(--enhanced-border-radius);
}

.enhanced-theme .rounded-xl {
    border-radius: var(--enhanced-border-radius-lg);
}

.enhanced-theme .shadow {
    box-shadow: var(--enhanced-shadow);
}

.enhanced-theme .shadow-md {
    box-shadow: var(--enhanced-shadow-md);
}

.enhanced-theme .shadow-lg {
    box-shadow: var(--enhanced-shadow-lg);
}

.enhanced-theme .shadow-xl {
    box-shadow: var(--enhanced-shadow-xl);
}

.enhanced-theme .transition {
    transition: var(--enhanced-transition);
}

.enhanced-theme .transition-fast {
    transition: var(--enhanced-transition-fast);
}

/* ===== SPECIAL EFFECTS ===== */

.enhanced-theme .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.enhanced-theme .gradient-text {
    background: linear-gradient(135deg, var(--enhanced-primary) 0%, var(--enhanced-primary-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.enhanced-theme .hover-lift {
    transition: var(--enhanced-transition);
}

.enhanced-theme .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--enhanced-shadow-lg);
}

.enhanced-theme .pulse {
    animation: enhanced-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes enhanced-pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* ===== THEME ACTIVATION ===== */

/*
To activate the enhanced theme, add the 'enhanced-theme' class to the body or app container:
<body class="enhanced-theme"> or <div id="app" class="app-container enhanced-theme">

This allows the enhanced styles to be easily toggled on/off without affecting the base styles.
*/
