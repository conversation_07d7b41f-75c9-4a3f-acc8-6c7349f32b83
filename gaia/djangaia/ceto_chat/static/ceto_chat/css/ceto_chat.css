/* Ceto Chat CSS v2 */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

.app-container {
    min-height: 100vh;
    position: relative;
    /* Make the page two columns (sidebar + main) */
    display: flex;
    align-items: stretch; /* ensure full height columns */
}

/* Left sidebar styles */
.sidebar {
    width: 280px;
    box-sizing: border-box;
    padding: 16px;
    background: #ffffff;
    border: 2px solid red; /* visual distinction as requested */
}

/* Conversation list */
.conv-item { display:flex; align-items:center; justify-content:space-between; gap:6px; border:1px solid #eee; border-radius:4px; padding:6px 8px; margin:4px 0; cursor:pointer; }
.conv-item:hover { background:#fafafa; }
.conv-item.active { background:#f0f7ff; border-color:#b6daff; }
.conv-title { flex:1; font-size: 13px; color:#333; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; }
.conv-delete { background: transparent; border: none; color:#999; font-size:16px; line-height:1; cursor:pointer; padding:2px 6px; }
.conv-delete:hover { color:#c00; }

.main-content {
    flex: 1;
    padding: 20px;
    transition: margin-right 0.3s ease;
    min-height: 100vh; /* full page height */
    box-sizing: border-box;
    background: #ffffff;
    border: 2px solid red; /* visual distinction as requested */
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
}

/* Basic button styles */
.btn {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    text-decoration: none;
    background-image: none;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
    background-color: transparent;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    color: #fff;
    background-color: #0056b3;
    border-color: #004085;
}

.btn-warning {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-outline-warning {
    color: #ffc107;
    border-color: #ffc107;
    background-color: transparent;
}

.btn-outline-warning:hover {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
    background-color: transparent;
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Debug toggle button */
.debug-toggle {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1050;
    background: #333 !important;
    color: white !important;
    border: 1px solid #666 !important;
}

.debug-toggle:hover {
    background: #444 !important;
    color: white !important;
}

/* Debug Panel Styles */
.debug-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 500px;
    height: 100vh;
    background: #1a1a1a;
    color: #e0e0e0;
    border-left: 1px solid #333;
    z-index: 1040;
    display: flex;
    flex-direction: column;
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.debug-header {
    padding: 10px;
    background: #2a2a2a;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.debug-header h5 {
    margin: 0;
    color: #fff;
    font-size: 14px;
}

.debug-controls {
    display: flex;
    gap: 5px;
}

.debug-controls .btn {
    font-size: 11px;
    padding: 2px 6px;
}

.debug-controls .btn-outline-warning {
    border-color: #ffc107;
    color: #ffc107;
    font-weight: bold;
}

.debug-controls .btn-outline-warning:hover {
    background-color: #ffc107;
    color: #212529;
    border-color: #ffc107;
}

.debug-tabs {
    display: flex;
    background: #2a2a2a;
    border-bottom: 1px solid #333;
    flex-wrap: wrap;
}

.debug-tab {
    flex: 1;
    padding: 8px 12px;
    background: #2a2a2a;
    color: #ccc;
    border: none;
    border-right: 1px solid #333;
    font-size: 11px;
    cursor: pointer;
    transition: background-color 0.2s;
    min-width: 80px;
}

.debug-tab:last-child {
    border-right: none;
}

.debug-tab:hover {
    background: #3a3a3a;
    color: #fff;
}

.debug-tab.active {
    background: #1a1a1a;
    color: #66ccff;
    border-bottom: 2px solid #66ccff;
}

.debug-content {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.debug-content h6 {
    margin: 0 0 10px 0;
    color: #fff;
    font-size: 12px;
    font-weight: bold;
}

.debug-empty {
    color: #666;
    text-align: center;
    padding: 20px;
    font-style: italic;
}

/* Call Details Table */
.call-details-table {
    display: flex;
    flex-direction: column;
    font-size: 10px;
}

.call-details-header {
    display: grid;
    grid-template-columns: 60px 50px 1fr 40px 50px 50px 60px;
    gap: 5px;
    padding: 5px;
    background: #333;
    border-radius: 3px;
    font-weight: bold;
    color: #ccc;
    margin-bottom: 5px;
}

.call-details-row {
    display: grid;
    grid-template-columns: 60px 50px 1fr 40px 50px 50px 60px;
    gap: 5px;
    padding: 3px 5px;
    border-radius: 2px;
    background: #1a1a1a;
    margin-bottom: 2px;
    color: #e0e0e0;
}

.call-details-row:hover {
    background: #333;
}

.call-details-row.call-error {
    background: #4a1a1a;
    color: #ffaaaa;
}

.call-time {
    font-size: 9px;
    color: #888;
}

.call-method {
    font-weight: bold;
    color: #66ccff;
}

.call-url {
    color: #66ccff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.call-url a { color:#66ccff; text-decoration:none; }
.call-url a:hover { text-decoration:underline; }
.call-row-details { grid-column:1 / -1; background:#0f0f0f; padding:6px 8px; border-left:2px solid #66ccff; margin:2px 0 4px; }
.call-detail-section h7 { display:block; color:#ccc; font-size:11px; font-weight:bold; margin:4px 0; }


.call-status {
    text-align: center;
    font-weight: bold;
}

.call-latency {
    text-align: right;
    color: #ffcc66;
}

.call-sent, .call-received {
    text-align: right;
    color: #ccffcc;
}

/* Debug Log Entries */
.debug-log-entry {
    margin-bottom: 15px;
    border: 1px solid #333;
    border-radius: 4px;
    background: #1a1a1a;
}

.debug-log-header {
    padding: 5px 10px;
    background: #2a2a2a;
    border-bottom: 1px solid #333;
    display: flex;
    gap: 10px;
    align-items: center;
    font-size: 10px;
}

.debug-timestamp {
    color: #888;
    font-family: monospace;
}

.debug-type {
    padding: 2px 6px;
    border-radius: 2px;
    font-weight: bold;
    font-size: 9px;
}

.debug-type-http {
    background: #0066cc;
    color: white;
}

.debug-type-websocket {
    background: #cc6600;
    color: white;
}

.debug-type-error {
    background: #cc0000;
    color: white;
}

.debug-type-system {
    background: #00cc66;
    color: white;
}

.debug-type-test {
    background: #cc00cc;
    color: white;
}

.debug-direction {
    padding: 2px 6px;
    border-radius: 2px;
    font-weight: bold;
    font-size: 9px;
}

.debug-direction-sent {
    background: #ff6666;
    color: white;
}

.debug-direction-received {
    background: #66ff66;
    color: black;
}

.debug-direction-info {
    background: #6666ff;
    color: white;
}

.debug-url {
    color: #66ccff;
    font-family: monospace;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
}

.debug-log-data {
    padding: 10px;
    margin: 0;
    background: #0a0a0a;
    color: #e0e0e0;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
}

.error-text {
    color: #ffaaaa;
    background: #2a0a0a;
}

/* Badge for error count */
.badge {
    display: inline-block;
    padding: 2px 6px;
    font-size: 9px;
    font-weight: bold;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 10px;
    margin-left: 5px;
}

.badge-danger {
    background-color: #dc3545;
}

/* Responsive adjustments */
.debug-panel-open .main-content {
    margin-right: 500px;
}

@media (max-width: 1200px) {
    .debug-panel {
        width: 400px;
    }

    .debug-panel-open .main-content {
        margin-right: 400px;
    }
}

@media (max-width: 768px) {
    .debug-panel {
        width: 100%;
        height: 50vh;
        top: auto;
        bottom: 0;
    }

    .debug-panel-open .main-content {
        margin-right: 0;
        height: 50vh;
    }

    .debug-tabs {
        flex-wrap: wrap;
    }

    .debug-tab {
        min-width: 60px;
        font-size: 10px;
        padding: 6px 8px;
    }
}

/* Shared chat UI elements */
.response-box { background:#f5f5f5; border:1px solid #ddd; border-radius:6px; padding:12px; white-space:pre-wrap; }
.chat-input-row { display:flex; gap:10px; align-items:center; flex-wrap:wrap; }
.chat-input-row input[type="text"] { flex: 1 1 420px; padding:8px; border:1px solid #ccc; border-radius:4px; }
.message-item { margin:6px 0; }
.message-item .role { font-weight:600; margin-right:6px; color:#555; }

