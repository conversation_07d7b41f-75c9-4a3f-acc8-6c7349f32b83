<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON> Chat – Tools + Raw Chat</title>

  <!-- Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <!-- Reuse existing assets -->
  <link rel="stylesheet" type="text/css" href="/gaia/djangaia/ceto_chat/static/ceto_chat/css/ceto_chat.css">

  <script>window.MCP_API_BASE = 'http://127.0.0.1:9000';</script>

  <!-- Vue ESM via import map -->
  <script type="importmap">
  {
    "imports": {
      "vue": "https://unpkg.com/vue@3.4.38/dist/vue.esm-browser.js"
    }
  }
  </script>
  <!-- ESM entrypoint -->
  <script type="module" src="/gaia/djangaia/ceto_chat/static/ceto_chat/ceto_app.module.js"></script>
  <script type="module" src="/gaia/djangaia/ceto_chat/static/ceto_chat/ui_flourishes.js"></script>
  <style>
    .response-box { background:#f5f5f5; border:1px solid #ddd; border-radius:6px; padding:12px; white-space:pre-wrap; }
    .chat-input-row { display:flex; gap:10px; align-items:center; flex-wrap:wrap; }
    .chat-input-row input[type="text"] { flex: 1 1 420px; padding:8px; border:1px solid #ccc; border-radius:4px; }
    .message-item { margin:6px 0; }
    .message-item .role { font-weight:600; margin-right:6px; color:#555; }
  </style>
</head>
<body>
  <div id="app" class="app-container">
    <!-- Debug toggle -->
    <button @click="toggleDebugPanel"
            class="btn btn-sm debug-toggle"
            :class="debugPanelVisible ? 'btn-warning' : 'btn-outline-secondary'"
            style="position: fixed; top: 10px; right: 10px; z-index: 1050; background: #333; color: white; border: 1px solid #666;">
      <i class="fas fa-bug"></i> Debug
    </button>

    <div class="main-content">
      <h1 style="display:flex; align-items:center; gap:8px;">
        Ceto Chat – Tools + Raw Chat
        <span v-if="mockModeActive"
              style="padding: 3px 8px; font-size: 12px; line-height: 1; border-radius: 10px; background:#343a40; color:#ffc107; border:1px solid #6c757d;">
          Mock mode
        </span>
        <span v-if="chatProviderName"
              style="padding: 3px 8px; font-size: 12px; line-height: 1; border-radius: 10px; background:#e2e3e5; color:#495057; border:1px solid #ced4da;">
          Provider: {{ chatProviderName }}
        </span>
      </h1>

      <!-- Connection status + Refresh tools -->
      <div class="mcp-status-bar" style="margin: 10px 0; padding: 8px; border-radius: 4px; font-size: 14px;"
           :style="{ backgroundColor: mcpConnected ? '#d4edda' : '#f8d7da',
                     color: mcpConnected ? '#155724' : '#721c24',
                     border: mcpConnected ? '1px solid #c3e6cb' : '1px solid #f5c6cb' }">
        <i :class="mcpConnected ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle'"></i>
        <span v-if="mcpLoading">Checking MCP server...</span>
        <span v-else-if="mcpConnected">Connected to MCP server at {{ mcpServerUrl || 'http://localhost:9000' }}</span>
        <span v-else>MCP server unavailable - mock fallback active</span>
        <button @click="loadMcpTools"
                class="btn btn-sm btn-outline-secondary"
                style="float: right; margin-top: -2px; font-size: 12px; padding: 2px 6px;"
                :disabled="mcpLoading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': mcpLoading }"></i> Refresh
        </button>
      </div>

      <!-- Chat layout: scrollable transcript + sticky input bar -->
      <div class="chat-pane" style="display:flex; flex-direction:column; height: calc(100vh - 220px); gap: 10px;">
        <!-- Transcript area -->
        <div id="chat-transcript" style="flex:1; overflow-y:auto; border:1px solid #eee; border-radius:4px; padding:10px;">
          <!-- Selected tool info -->
          <div v-if="selectedToolName" style="margin-bottom: 6px; color: #666;">
            <small v-if="selectedToolName === 'raw_chat'"><strong>Selected:</strong> raw_chat — Direct chat via MCP HTTP (fallback to mock_chat when unavailable)</small>
            <small v-else><strong>Selected:</strong> <span v-text="selectedToolName"></span>
              <span v-if="mcpTools.find(t => t.name === selectedToolName)?.description"
                    v-text="' — ' + mcpTools.find(t => t.name === selectedToolName).description"></span>
            </small>
          </div>
          <!-- Single ordered view combining chat and tool responses -->
          <div>
            <div v-for="item in renderItems" :key="item.id" style="margin: 6px 0;">
              <template v-if="item.kind === 'chat'">
                <div class="message-item"><span class="role">{{ item.role }}:</span> <span v-text="item.text"></span></div>
              </template>
              <template v-else-if="item.kind === 'tool'">
                <small v-if="item.status === 'pending' && !item.result" style="color:#666;">Tool: {{ item.toolName }} — running…</small>
                <chat-response-panel v-else :response="item.result" :show-transcript="false" v-model="showFullJsonRpc"></chat-response-panel>
              </template>
            </div>
          </div>
        </div>
        <!-- Sticky input bar -->
        <div class="chat-input" style="position: sticky; bottom: 0; background: #fff; padding: 8px 0; border-top: 1px solid #eee;">
          <div style="display:flex; gap: 10px; align-items:center; flex-wrap: wrap;">
            <input v-model="userMessage"
                   @keydown.enter.prevent="handleSendUnified"
                   type="text"
                   placeholder="Message… Type / for tools"
                   style="padding:8px; flex:1 1 360px; border:1px solid #ccc; border-radius:3px;">
            <button @click="handleSendUnified"
                    :disabled="(sending || mcpToolLoading) || !userMessage.trim()"
                    style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;"
                    :style="{ opacity: ((sending || mcpToolLoading) || !userMessage.trim()) ? 0.6 : 1 }">
              <span v-if="sending || mcpToolLoading"><i class="fas fa-spinner fa-spin"></i> Sending…</span>
              <span v-else>Send</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Debug Panel -->
    <debug-panel :show-tools-tab="true"></debug-panel>
  </div>
</body>
</html>